{% extends "base.html" %}

{% block title %}الكورسات - {{ platform_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="text-center mb-4">
        <h1 class="display-5 fw-bold text-primary">الكورسات</h1>
        <p class="lead text-muted">اكتشف الكورسات المتاحة وتابع تقدمك في الكورسات المسجل بها</p>
    </div>

    <!-- تبويبات الكورسات -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-pills nav-fill" id="courseTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="pill" data-bs-target="#all-courses" type="button" role="tab">
                        <i class="fas fa-th-large me-2"></i>جميع الكورسات
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="enrolled-tab" data-bs-toggle="pill" data-bs-target="#enrolled-courses" type="button" role="tab">
                        <i class="fas fa-bookmark me-2"></i>كورساتي
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="available-tab" data-bs-toggle="pill" data-bs-target="#available-courses" type="button" role="tab">
                        <i class="fas fa-plus-circle me-2"></i>متاحة للتسجيل
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label small text-muted">التخصص</label>
                    <select class="form-select" id="specializationFilter">
                        <option value="">جميع التخصصات</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label small text-muted">المرحلة</label>
                    <select class="form-select" id="stageFilter">
                        <option value="">جميع المراحل</option>
                        <option value="2">المرحلة الثانية</option>
                        <option value="3">المرحلة الثالثة</option>
                        <option value="4">المرحلة الرابعة</option>
                        <option value="general">عام</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label small text-muted">البحث</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" placeholder="البحث في الكورسات...">
                        <button class="btn btn-outline-primary" type="button" id="searchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label small text-muted">&nbsp;</label>
                    <button class="btn btn-outline-secondary w-100" id="clearFilters">
                        <i class="fas fa-times me-1"></i>مسح
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- محتوى التبويبات -->
    <div class="tab-content" id="courseTabsContent">
        <!-- جميع الكورسات -->
        <div class="tab-pane fade show active" id="all-courses" role="tabpanel">
            <div id="allCoursesContainer">
                <!-- سيتم تحميل الكورسات هنا -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- كورساتي -->
        <div class="tab-pane fade" id="enrolled-courses" role="tabpanel">
            <div id="enrolledCoursesContainer">
                <!-- سيتم تحميل الكورسات المسجل بها هنا -->
            </div>
        </div>

        <!-- متاحة للتسجيل -->
        <div class="tab-pane fade" id="available-courses" role="tabpanel">
            <div id="availableCoursesContainer">
                <!-- سيتم تحميل الكورسات المتاحة هنا -->
            </div>
        </div>
    </div>

    <!-- إحصائيات -->
    <div class="row mt-4" id="courseStats" style="display: none;">
        <div class="col-12">
            <div class="card border-0 bg-light">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="fas fa-book text-primary me-2"></i>
                                <span><strong id="totalCoursesCount">0</strong> إجمالي الكورسات</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="fas fa-bookmark text-success me-2"></i>
                                <span><strong id="enrolledCoursesCount">0</strong> كورسات مسجل بها</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="fas fa-plus-circle text-info me-2"></i>
                                <span><strong id="availableCoursesCount">0</strong> متاحة للتسجيل</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتفعيل كورس -->
<div class="modal fade" id="activateCourseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفعيل كورس جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="activateCourseForm">
                    <div class="mb-3">
                        <label for="activationCode" class="form-label">كود التفعيل</label>
                        <input type="text" class="form-control" id="activationCode" placeholder="أدخل كود التفعيل" required>
                        <div class="form-text">أدخل كود التفعيل الذي حصلت عليه من المدرس</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="activateCourseBtn">تفعيل الكورس</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadSpecializations();
    loadCourses();

    // مستمعي الأحداث للفلاتر
    $('#specializationFilter, #stageFilter').change(function() {
        loadCourses();
    });

    $('#searchBtn').click(function() {
        loadCourses();
    });

    $('#searchInput').keypress(function(e) {
        if (e.which === 13) {
            loadCourses();
        }
    });

    $('#clearFilters').click(function() {
        $('#specializationFilter').val('');
        $('#stageFilter').val('');
        $('#searchInput').val('');
        loadCourses();
    });

    // مستمعي الأحداث للتبويبات
    $('button[data-bs-toggle="pill"]').on('shown.bs.tab', function(e) {
        const target = $(e.target).attr('data-bs-target');
        loadCoursesForTab(target);
    });

    // تفعيل كورس جديد
    $('#activateCourseBtn').click(function() {
        const code = $('#activationCode').val().trim();
        if (!code) {
            showAlert('يرجى إدخال كود التفعيل', 'warning');
            return;
        }

        activateCourse(code);
    });
});

async function loadSpecializations() {
    try {
        const response = await fetch('/api/specializations');
        const data = await response.json();

        if (data.success) {
            const select = $('#specializationFilter');
            data.specializations.forEach(spec => {
                select.append(`<option value="${spec.id}">${spec.name}</option>`);
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل التخصصات:', error);
    }
}

async function loadCourses() {
    const params = new URLSearchParams({
        search: $('#searchInput').val(),
        specialization: $('#specializationFilter').val(),
        stage: $('#stageFilter').val()
    });

    try {
        const response = await fetch(`/api/student/courses?${params}`);
        const data = await response.json();

        if (data.success) {
            displayCourses(data.data.courses, '#allCoursesContainer');
            updateStats(data.data.stats);

            // تحديث التبويبات الأخرى
            const enrolledCourses = data.data.courses.filter(c => c.enrollment_info);
            const availableCourses = data.data.courses.filter(c => !c.enrollment_info);

            displayCourses(enrolledCourses, '#enrolledCoursesContainer');
            displayCourses(availableCourses, '#availableCoursesContainer');
        } else {
            showAlert('فشل في تحميل الكورسات: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('خطأ في تحميل الكورسات:', error);
        showAlert('حدث خطأ في تحميل الكورسات', 'danger');
    }
}

function loadCoursesForTab(tabId) {
    const status = tabId === '#enrolled-courses' ? 'enrolled' :
                  tabId === '#available-courses' ? 'available' : '';

    if (status) {
        const params = new URLSearchParams({
            search: $('#searchInput').val(),
            specialization: $('#specializationFilter').val(),
            stage: $('#stageFilter').val(),
            status: status
        });

        fetch(`/api/student/courses?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const containerId = tabId.replace('#', '#') + 'Container';
                    displayCourses(data.data.courses, containerId);
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل الكورسات:', error);
            });
    }
}

function displayCourses(courses, containerId) {
    const container = $(containerId);

    if (!courses || courses.length === 0) {
        container.html(`
            <div class="text-center py-5">
                <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد كورسات</h5>
                <p class="text-muted">لا توجد كورسات تطابق معايير البحث</p>
                ${containerId === '#availableCoursesContainer' ? `
                    <button class="btn btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#activateCourseModal">
                        <i class="fas fa-plus me-2"></i>تفعيل كورس جديد
                    </button>
                ` : ''}
            </div>
        `);
        return;
    }

    let html = '<div class="row">';

    courses.forEach(function(course) {
        const isEnrolled = !!course.enrollment_info;
        const progress = isEnrolled ? course.enrollment_info.progress || {} : {};
        const completionPercentage = progress.completion_percentage || 0;

        html += `
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-sm course-card" data-course-id="${course.id}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h6 class="card-title mb-0">${course.title}</h6>
                            ${isEnrolled ? `
                                <span class="badge bg-success rounded-pill">مسجل</span>
                            ` : `
                                <span class="badge bg-primary rounded-pill">متاح</span>
                            `}
                        </div>

                        <p class="card-text text-muted small mb-2">
                            <i class="fas fa-user me-1"></i>
                            ${course.instructor_name || 'غير محدد'}
                        </p>

                        <p class="card-text text-muted small mb-3">
                            <i class="fas fa-layer-group me-1"></i>
                            ${course.is_general ? 'كورس عام' : `المرحلة ${course.stage}`}
                        </p>

                        ${course.description ? `
                            <p class="card-text small text-muted mb-3">${course.description.substring(0, 100)}${course.description.length > 100 ? '...' : ''}</p>
                        ` : ''}

                        ${isEnrolled ? `
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small class="text-muted">التقدم</small>
                                    <small class="text-muted">${completionPercentage}%</small>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar ${completionPercentage >= 100 ? 'bg-success' : 'bg-primary'}"
                                         style="width: ${completionPercentage}%"></div>
                                </div>
                            </div>

                            <div class="d-grid">
                                <a href="/student/course/${course.id}" class="btn btn-primary btn-sm">
                                    ${completionPercentage > 0 ? 'متابعة الدراسة' : 'بدء الدراسة'}
                                    <i class="fas fa-arrow-left ms-1"></i>
                                </a>
                            </div>
                        ` : `
                            <div class="d-grid">
                                <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#activateCourseModal">
                                    <i class="fas fa-key me-1"></i>تفعيل الكورس
                                </button>
                            </div>
                        `}
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.html(html);
}

function updateStats(stats) {
    $('#totalCoursesCount').text(stats.total || 0);
    $('#enrolledCoursesCount').text(stats.enrolled || 0);
    $('#availableCoursesCount').text(stats.available || 0);
    $('#courseStats').show();
}

function activateCourse(code) {
    const btn = $('#activateCourseBtn');
    const originalText = btn.text();

    btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>جاري التفعيل...');

    $.ajax({
        url: '/api/activation-codes/use',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ code: code }),
        success: function(response) {
            if (response.success) {
                showAlert('تم تفعيل الكورس بنجاح!', 'success');
                $('#activateCourseModal').modal('hide');
                $('#activationCode').val('');
                loadCourses(); // إعادة تحميل الكورسات
            } else {
                showAlert('فشل في تفعيل الكورس: ' + response.message, 'danger');
            }
        },
        error: function() {
            showAlert('حدث خطأ في تفعيل الكورس', 'danger');
        },
        complete: function() {
            btn.prop('disabled', false).text(originalText);
        }
    });
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('.container').first().prepend(alertHtml);

    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
