/**
 * محرر المحتوى الغني للكورسات
 * Rich Content Editor for Courses
 */

// وظيفة مساعدة للطلبات مع المصادقة
async function fetchWithAuth(url, options = {}) {
    const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');

    const defaultOptions = {
        headers: {
            'Authorization': `Bearer ${token}`,
            ...options.headers
        }
    };

    const response = await fetch(url, { ...options, ...defaultOptions });

    if (response.status === 401) {
        // إعادة توجيه لصفحة تسجيل الدخول
        window.location.href = '/login';
        return;
    }

    const data = await response.json();
    return data;
}

class ContentEditor {
    constructor() {
        this.courseId = this.getCourseIdFromUrl();
        this.currentLessonId = null;
        this.currentContentType = null;
        this.lessons = [];
        this.quillEditor = null;
        this.sortable = null;
        
        this.init();
    }
    
    async init() {
        try {
            // تهيئة محرر النصوص الغني
            this.initQuillEditor();
            
            // تهيئة الأحداث
            this.setupEventListeners();
            
            // تهيئة السحب والإفلات
            this.initSortable();
            
            // تحميل الدروس الموجودة
            await this.loadLessons();
            
        } catch (error) {
            console.error('خطأ في تهيئة محرر المحتوى:', error);
            this.showError('حدث خطأ في تحميل المحرر');
        }
    }
    
    getCourseIdFromUrl() {
        const pathParts = window.location.pathname.split('/');
        return pathParts[pathParts.length - 2]; // افتراض أن الرابط /instructor/courses/{id}/edit
    }
    
    initQuillEditor() {
        this.quillEditor = new Quill('#textContent', {
            theme: 'snow',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    [{ 'align': [] }],
                    ['link', 'blockquote', 'code-block'],
                    ['clean']
                ]
            },
            placeholder: 'اكتب محتوى الدرس هنا...'
        });
    }
    
    setupEventListeners() {
        // أزرار أنواع المحتوى
        document.querySelectorAll('.content-type-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const contentType = e.target.closest('.content-type-btn').dataset.type;
                this.showContentEditor(contentType);
            });
        });
        
        // نماذج المحتوى
        document.getElementById('textForm').addEventListener('submit', (e) => this.saveTextLesson(e));
        document.getElementById('imageForm').addEventListener('submit', (e) => this.saveImageLesson(e));
        document.getElementById('videoForm').addEventListener('submit', (e) => this.saveVideoLesson(e));
        document.getElementById('mcqForm').addEventListener('submit', (e) => this.saveMcqLesson(e));
        document.getElementById('essayForm').addEventListener('submit', (e) => this.saveEssayLesson(e));
        
        // رفع الصور
        this.setupImageUpload();
    }
    
    setupImageUpload() {
        const uploadArea = document.getElementById('imageUploadArea');
        const fileInput = document.getElementById('imageUpload');
        const preview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        
        // النقر على منطقة الرفع
        uploadArea.addEventListener('click', () => fileInput.click());
        
        // تغيير الملف
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.previewImage(file, previewImg, preview);
            }
        });
        
        // السحب والإفلات
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const file = e.dataTransfer.files[0];
            if (file && file.type.startsWith('image/')) {
                fileInput.files = e.dataTransfer.files;
                this.previewImage(file, previewImg, preview);
            }
        });
    }
    
    previewImage(file, imgElement, containerElement) {
        const reader = new FileReader();
        reader.onload = (e) => {
            imgElement.src = e.target.result;
            containerElement.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
    
    initSortable() {
        const lessonsList = document.getElementById('lessonsList');
        this.sortable = Sortable.create(lessonsList, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            handle: '.drag-handle',
            onEnd: (evt) => {
                this.updateLessonsOrder();
            }
        });
    }
    
    showContentEditor(contentType) {
        // إخفاء جميع المحررات
        document.querySelectorAll('.content-editor-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        // إزالة التفعيل من جميع الأزرار
        document.querySelectorAll('.content-type-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // تفعيل المحرر المطلوب
        const editorId = contentType + 'Editor';
        const editor = document.getElementById(editorId);
        if (editor) {
            editor.classList.add('active');
        }
        
        // تفعيل الزر المطلوب
        const activeBtn = document.querySelector(`[data-type="${contentType}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
        
        this.currentContentType = contentType;
        this.currentLessonId = null; // إنشاء درس جديد
        
        // مسح النماذج
        this.clearForms();
    }
    
    clearForms() {
        // مسح نموذج النص
        document.getElementById('textTitle').value = '';
        if (this.quillEditor) {
            this.quillEditor.setContents([]);
        }
        
        // مسح نموذج الصورة
        document.getElementById('imageTitle').value = '';
        document.getElementById('imageCaption').value = '';
        document.getElementById('imageUpload').value = '';
        document.getElementById('imagePreview').style.display = 'none';
        
        // مسح نموذج الفيديو
        document.getElementById('videoTitle').value = '';
        document.getElementById('videoUrl').value = '';
        document.getElementById('videoDescription').value = '';
        document.getElementById('videoDuration').value = '';
        
        // مسح نموذج الأسئلة متعددة الخيارات
        document.getElementById('mcqTitle').value = '';
        document.getElementById('mcqQuestion').value = '';
        document.getElementById('mcqFeedback').value = '';
        document.getElementById('mcqPoints').value = '1';
        this.resetMcqOptions();
        
        // مسح نموذج الأسئلة المقالية
        document.getElementById('essayTitle').value = '';
        document.getElementById('essayQuestion').value = '';
        document.getElementById('essayMaxWords').value = '500';
        document.getElementById('essayRubric').value = '';
        document.getElementById('essayPoints').value = '10';
    }
    
    resetMcqOptions() {
        const optionsContainer = document.getElementById('mcqOptions');
        optionsContainer.innerHTML = `
            <div class="option-input">
                <input type="text" class="form-control" placeholder="الخيار الأول" required>
                <input type="checkbox" class="form-check-input" title="إجابة صحيحة">
                <button type="button" class="remove-option-btn" onclick="removeOption(this)" style="display: none;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="option-input">
                <input type="text" class="form-control" placeholder="الخيار الثاني" required>
                <input type="checkbox" class="form-check-input" title="إجابة صحيحة">
                <button type="button" class="remove-option-btn" onclick="removeOption(this)" style="display: none;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        this.updateRemoveButtons();
    }
    
    async loadLessons() {
        try {
            this.showLoading(true);
            
            const response = await fetchWithAuth(`/api/instructor/courses/${this.courseId}/lessons`);
            
            if (response.success) {
                this.lessons = response.lessons || [];
                this.renderLessons();
            } else {
                throw new Error(response.message || 'فشل في تحميل الدروس');
            }
            
        } catch (error) {
            console.error('خطأ في تحميل الدروس:', error);
            this.showError('حدث خطأ في تحميل الدروس');
        } finally {
            this.showLoading(false);
        }
    }
    
    renderLessons() {
        const lessonsList = document.getElementById('lessonsList');
        
        if (this.lessons.length === 0) {
            lessonsList.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-book-open fa-3x mb-3"></i>
                    <p>لا توجد دروس بعد</p>
                    <p class="small">ابدأ بإضافة محتوى جديد</p>
                </div>
            `;
            return;
        }
        
        lessonsList.innerHTML = this.lessons.map((lesson, index) => `
            <div class="lesson-item" data-lesson-id="${lesson.id}">
                <div class="lesson-header">
                    <i class="fas fa-grip-vertical drag-handle"></i>
                    <h6 class="lesson-title">${lesson.title}</h6>
                    <span class="lesson-type-badge">${this.getContentTypeLabel(lesson.content_type)}</span>
                </div>
                <div class="lesson-actions">
                    <button class="lesson-btn" onclick="contentEditor.editLesson('${lesson.id}')">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="lesson-btn" onclick="contentEditor.duplicateLesson('${lesson.id}')">
                        <i class="fas fa-copy"></i> نسخ
                    </button>
                    <button class="lesson-btn danger" onclick="contentEditor.deleteLesson('${lesson.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `).join('');
    }
    
    getContentTypeLabel(contentType) {
        const labels = {
            'text': 'نص',
            'image': 'صورة',
            'video': 'فيديو',
            'mcq': 'أسئلة متعددة',
            'essay': 'سؤال مقالي'
        };
        return labels[contentType] || contentType;
    }
    
    async saveTextLesson(e) {
        e.preventDefault();
        
        const title = document.getElementById('textTitle').value.trim();
        const content = this.quillEditor.getContents();
        
        if (!title) {
            this.showError('عنوان الدرس مطلوب');
            return;
        }
        
        const lessonData = {
            title: title,
            content_type: 'text',
            content_data: {
                content: content,
                html: this.quillEditor.root.innerHTML
            }
        };
        
        await this.saveLesson(lessonData);
    }
    
    async saveImageLesson(e) {
        e.preventDefault();
        
        const title = document.getElementById('imageTitle').value.trim();
        const caption = document.getElementById('imageCaption').value.trim();
        const fileInput = document.getElementById('imageUpload');
        
        if (!title) {
            this.showError('عنوان الدرس مطلوب');
            return;
        }
        
        if (!fileInput.files[0]) {
            this.showError('يجب اختيار صورة');
            return;
        }
        
        try {
            this.showLoading(true);
            
            // رفع الصورة أولاً
            const imageUrl = await this.uploadImage(fileInput.files[0]);
            
            const lessonData = {
                title: title,
                content_type: 'image',
                content_data: {
                    image_url: imageUrl,
                    caption: caption,
                    alt_text: title
                }
            };
            
            await this.saveLesson(lessonData);
            
        } catch (error) {
            console.error('خطأ في حفظ درس الصورة:', error);
            this.showError('حدث خطأ في رفع الصورة');
        } finally {
            this.showLoading(false);
        }
    }
    
    async saveVideoLesson(e) {
        e.preventDefault();
        
        const title = document.getElementById('videoTitle').value.trim();
        const videoUrl = document.getElementById('videoUrl').value.trim();
        const description = document.getElementById('videoDescription').value.trim();
        const duration = parseInt(document.getElementById('videoDuration').value) || 0;
        
        if (!title || !videoUrl) {
            this.showError('عنوان الدرس ورابط الفيديو مطلوبان');
            return;
        }
        
        // استخراج معرف الفيديو من رابط YouTube
        const videoId = this.extractYouTubeId(videoUrl);
        if (!videoId) {
            this.showError('رابط YouTube غير صحيح');
            return;
        }
        
        const lessonData = {
            title: title,
            content_type: 'video',
            content_data: {
                youtube_id: videoId,
                description: description,
                duration: duration * 60 // تحويل إلى ثواني
            }
        };
        
        await this.saveLesson(lessonData);
    }
    
    extractYouTubeId(url) {
        const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
        const match = url.match(regExp);
        return (match && match[2].length === 11) ? match[2] : null;
    }
    
    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        spinner.style.display = show ? 'block' : 'none';
    }
    
    showError(message) {
        this.showMessage(message, 'error');
    }
    
    showSuccess(message) {
        this.showMessage(message, 'success');
    }
    
    showMessage(message, type) {
        const container = document.getElementById('messageContainer');
        const alertClass = type === 'error' ? 'error-message' : 'success-message';

        container.innerHTML = `
            <div class="${alertClass}">
                <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : 'check-circle'} me-2"></i>
                ${message}
            </div>
        `;

        // إخفاء الرسالة بعد 5 ثوان
        setTimeout(() => {
            container.innerHTML = '';
        }, 5000);
    }

    async saveMcqLesson(e) {
        e.preventDefault();

        const title = document.getElementById('mcqTitle').value.trim();
        const question = document.getElementById('mcqQuestion').value.trim();
        const feedback = document.getElementById('mcqFeedback').value.trim();
        const points = parseInt(document.getElementById('mcqPoints').value) || 1;

        if (!title || !question) {
            this.showError('عنوان الدرس ونص السؤال مطلوبان');
            return;
        }

        // جمع الخيارات
        const options = [];
        const correctAnswers = [];
        const optionInputs = document.querySelectorAll('#mcqOptions .option-input');

        optionInputs.forEach((optionDiv, index) => {
            const textInput = optionDiv.querySelector('input[type="text"]');
            const checkbox = optionDiv.querySelector('input[type="checkbox"]');

            if (textInput.value.trim()) {
                options.push(textInput.value.trim());
                if (checkbox.checked) {
                    correctAnswers.push(index);
                }
            }
        });

        if (options.length < 2) {
            this.showError('يجب إضافة خيارين على الأقل');
            return;
        }

        if (correctAnswers.length === 0) {
            this.showError('يجب تحديد إجابة صحيحة واحدة على الأقل');
            return;
        }

        const lessonData = {
            title: title,
            content_type: 'mcq',
            content_data: {
                question: question,
                options: options,
                correct_answers: correctAnswers,
                feedback: feedback,
                points: points
            }
        };

        await this.saveLesson(lessonData);
    }

    async saveEssayLesson(e) {
        e.preventDefault();

        const title = document.getElementById('essayTitle').value.trim();
        const question = document.getElementById('essayQuestion').value.trim();
        const maxWords = parseInt(document.getElementById('essayMaxWords').value) || 500;
        const rubric = document.getElementById('essayRubric').value.trim();
        const points = parseInt(document.getElementById('essayPoints').value) || 10;

        if (!title || !question) {
            this.showError('عنوان الدرس ونص السؤال مطلوبان');
            return;
        }

        const lessonData = {
            title: title,
            content_type: 'essay',
            content_data: {
                question: question,
                max_words: maxWords,
                rubric: rubric,
                points: points
            }
        };

        await this.saveLesson(lessonData);
    }

    async saveLesson(lessonData) {
        try {
            this.showLoading(true);

            const url = this.currentLessonId ?
                `/api/instructor/courses/${this.courseId}/lessons/${this.currentLessonId}` :
                `/api/instructor/courses/${this.courseId}/lessons`;

            const method = this.currentLessonId ? 'PUT' : 'POST';

            // إضافة ترتيب الدرس
            if (!this.currentLessonId) {
                lessonData.order = this.lessons.length + 1;
            }

            const response = await fetchWithAuth(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(lessonData)
            });

            if (response.success) {
                this.showSuccess(this.currentLessonId ? 'تم تحديث الدرس بنجاح' : 'تم إضافة الدرس بنجاح');

                // إعادة تحميل الدروس
                await this.loadLessons();

                // إخفاء المحرر
                cancelEdit();

            } else {
                throw new Error(response.message || 'فشل في حفظ الدرس');
            }

        } catch (error) {
            console.error('خطأ في حفظ الدرس:', error);
            this.showError('حدث خطأ في حفظ الدرس');
        } finally {
            this.showLoading(false);
        }
    }

    async uploadImage(file) {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('course_id', this.courseId);

        const response = await fetchWithAuth('/api/instructor/upload-image', {
            method: 'POST',
            body: formData
        });

        if (response.success) {
            return response.image_url;
        } else {
            throw new Error(response.message || 'فشل في رفع الصورة');
        }
    }

    async editLesson(lessonId) {
        const lesson = this.lessons.find(l => l.id === lessonId);
        if (!lesson) return;

        this.currentLessonId = lessonId;
        this.showContentEditor(lesson.content_type);

        // ملء النموذج بالبيانات الموجودة
        this.fillFormWithLessonData(lesson);
    }

    fillFormWithLessonData(lesson) {
        const contentData = lesson.content_data;

        switch (lesson.content_type) {
            case 'text':
                document.getElementById('textTitle').value = lesson.title;
                if (this.quillEditor && contentData.content) {
                    this.quillEditor.setContents(contentData.content);
                }
                break;

            case 'image':
                document.getElementById('imageTitle').value = lesson.title;
                document.getElementById('imageCaption').value = contentData.caption || '';
                if (contentData.image_url) {
                    document.getElementById('previewImg').src = contentData.image_url;
                    document.getElementById('imagePreview').style.display = 'block';
                }
                break;

            case 'video':
                document.getElementById('videoTitle').value = lesson.title;
                document.getElementById('videoUrl').value = `https://www.youtube.com/watch?v=${contentData.youtube_id}`;
                document.getElementById('videoDescription').value = contentData.description || '';
                document.getElementById('videoDuration').value = Math.floor((contentData.duration || 0) / 60);
                break;

            case 'mcq':
                document.getElementById('mcqTitle').value = lesson.title;
                document.getElementById('mcqQuestion').value = contentData.question;
                document.getElementById('mcqFeedback').value = contentData.feedback || '';
                document.getElementById('mcqPoints').value = contentData.points || 1;
                this.fillMcqOptions(contentData.options, contentData.correct_answers);
                break;

            case 'essay':
                document.getElementById('essayTitle').value = lesson.title;
                document.getElementById('essayQuestion').value = contentData.question;
                document.getElementById('essayMaxWords').value = contentData.max_words || 500;
                document.getElementById('essayRubric').value = contentData.rubric || '';
                document.getElementById('essayPoints').value = contentData.points || 10;
                break;
        }
    }

    fillMcqOptions(options, correctAnswers) {
        const optionsContainer = document.getElementById('mcqOptions');
        optionsContainer.innerHTML = '';

        options.forEach((option, index) => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'option-input';
            optionDiv.innerHTML = `
                <input type="text" class="form-control" value="${option}" required>
                <input type="checkbox" class="form-check-input" title="إجابة صحيحة" ${correctAnswers.includes(index) ? 'checked' : ''}>
                <button type="button" class="remove-option-btn" onclick="removeOption(this)">
                    <i class="fas fa-times"></i>
                </button>
            `;
            optionsContainer.appendChild(optionDiv);
        });

        this.updateRemoveButtons();
    }

    updateRemoveButtons() {
        const optionInputs = document.querySelectorAll('#mcqOptions .option-input');
        optionInputs.forEach((optionDiv, index) => {
            const removeBtn = optionDiv.querySelector('.remove-option-btn');
            removeBtn.style.display = optionInputs.length > 2 ? 'inline-block' : 'none';
        });
    }

    async duplicateLesson(lessonId) {
        const lesson = this.lessons.find(l => l.id === lessonId);
        if (!lesson) return;

        const duplicatedLesson = {
            ...lesson,
            title: lesson.title + ' (نسخة)',
            order: this.lessons.length + 1
        };

        delete duplicatedLesson.id;

        await this.saveLesson(duplicatedLesson);
    }

    async deleteLesson(lessonId) {
        if (!confirm('هل أنت متأكد من حذف هذا الدرس؟')) {
            return;
        }

        try {
            this.showLoading(true);

            const response = await fetchWithAuth(`/api/instructor/courses/${this.courseId}/lessons/${lessonId}`, {
                method: 'DELETE'
            });

            if (response.success) {
                this.showSuccess('تم حذف الدرس بنجاح');
                await this.loadLessons();
            } else {
                throw new Error(response.message || 'فشل في حذف الدرس');
            }

        } catch (error) {
            console.error('خطأ في حذف الدرس:', error);
            this.showError('حدث خطأ في حذف الدرس');
        } finally {
            this.showLoading(false);
        }
    }

    async updateLessonsOrder() {
        const lessonItems = document.querySelectorAll('.lesson-item');
        const newOrder = Array.from(lessonItems).map((item, index) => ({
            id: item.dataset.lessonId,
            order: index + 1
        }));

        try {
            const response = await fetchWithAuth(`/api/instructor/courses/${this.courseId}/lessons/reorder`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ lessons: newOrder })
            });

            if (response.success) {
                // تحديث الترتيب محلياً
                this.lessons.forEach(lesson => {
                    const newOrderItem = newOrder.find(item => item.id === lesson.id);
                    if (newOrderItem) {
                        lesson.order = newOrderItem.order;
                    }
                });

                this.lessons.sort((a, b) => a.order - b.order);
            }

        } catch (error) {
            console.error('خطأ في تحديث ترتيب الدروس:', error);
            // إعادة تحميل الدروس في حالة الخطأ
            await this.loadLessons();
        }
    }
}

// متغير عام للوصول من HTML
let contentEditor;

// تهيئة المحرر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    contentEditor = new ContentEditor();
});

// وظائف عامة يمكن استدعاؤها من HTML
function cancelEdit() {
    // إخفاء جميع المحررات
    document.querySelectorAll('.content-editor-panel').forEach(panel => {
        panel.classList.remove('active');
    });
    
    // إزالة التفعيل من جميع الأزرار
    document.querySelectorAll('.content-type-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    contentEditor.currentContentType = null;
    contentEditor.currentLessonId = null;
}

function addMcqOption() {
    const optionsContainer = document.getElementById('mcqOptions');
    const optionDiv = document.createElement('div');
    optionDiv.className = 'option-input';
    optionDiv.innerHTML = `
        <input type="text" class="form-control" placeholder="خيار جديد" required>
        <input type="checkbox" class="form-check-input" title="إجابة صحيحة">
        <button type="button" class="remove-option-btn" onclick="removeOption(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    optionsContainer.appendChild(optionDiv);
    contentEditor.updateRemoveButtons();
}

function removeOption(button) {
    button.closest('.option-input').remove();
    contentEditor.updateRemoveButtons();
}

function saveDraft() {
    // حفظ الكورس كمسودة
    console.log('حفظ مسودة');
}

function publishCourse() {
    // نشر الكورس
    console.log('نشر الكورس');
}
