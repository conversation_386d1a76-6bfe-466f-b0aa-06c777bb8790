# 🎓 منصة الكورسات التعليمية - Educational Courses Platform

منصة تعليمية متطورة مشابهة لـ Coursera مع بوت تليجرام لإدارة الحسابات ونظام أمان متقدم، مخصصة للتخصصات الطبية.

## 🎯 نظرة عامة

منصة تعليمية شاملة تهدف إلى تقديم تعليم طبي متقدم في التخصصات التالية:
- 🔬 **التحليل الطبي** (المراحل 2، 3، 4)
- 📡 **الأشعة** (المراحل 2، 3، 4)  
- 💉 **التخدير** (المراحل 2، 3، 4)

## 🏗️ البنية التقنية

### الخادم الخلفي (Backend)
- **الإطار**: Flask (Python)
- **قاعدة البيانات**: Firebase Realtime Database
- **المصادقة**: Firebase Auth + JWT
- **تخزين الملفات**: Firebase Storage

### بوت التليجرام
- **المكتبة**: telebot (pyTelegramBotAPI)
- **الوظيفة**: إدارة إنشاء الحسابات والدعوات
- **المجلد**: `bot/` (منفصل عن الموقع)

### الواجهة الأمامية (Frontend)
- **الأساس**: HTML5, CSS3, JavaScript (ES6+)
- **إطار CSS**: Bootstrap 5 RTL
- **الأيقونات**: Font Awesome 6
- **الخطوط**: Cairo (للعربية)

## 🔐 نظام الأدوار

### 🤖 مالك البوت (Bot Owner)
- إنشاء حسابات المدرسين عبر البوت
- إدارة التخصصات وصلاحياتها
- إنشاء أكواد الوصول الشامل
- مراقبة النظام والتحليلات

### 👨‍🏫 المدرس (Instructor)
- إنشاء الكورسات ضمن صلاحياته
- إنتاج أكواد التفعيل للكورسات
- إنشاء روابط دعوة الطلاب
- إدارة الطلاب التابعين له

### 👨‍🎓 الطالب (Student)
- تصفح وتفعيل الكورسات
- الوصول للمحتوى التعليمي
- تتبع التقدم الشخصي
- مشاهدة الفيديوهات بمشغل متقدم

## 🚀 التثبيت والتشغيل

### 1. متطلبات النظام
```bash
Python 3.8+
pip
Git
```

### 2. تحميل المشروع
```bash
git clone <repository-url>
cd educational-courses-platform
```

### 3. تثبيت المكتبات
```bash
pip install -r requirements.txt
```

### 4. إعداد متغيرات البيئة
```bash
cp .env.example .env
# قم بتعديل ملف .env وإضافة البيانات المطلوبة
```

### 5. تشغيل التطبيق
```bash
# تشغيل الموقع
python app.py

# تشغيل البوت (في terminal منفصل)
python bot/main.py
```

## 📁 هيكل المشروع

```
/
├── app.py                 # التطبيق الرئيسي
├── config.py             # إعدادات التطبيق
├── requirements.txt      # المكتبات المطلوبة
├── .env.example         # مثال متغيرات البيئة
├── .gitignore           # ملفات مستبعدة من Git
├── README.md            # هذا الملف
├── templates/           # قوالب HTML
│   ├── base.html       # القالب الأساسي
│   ├── index.html      # الصفحة الرئيسية
│   ├── auth/           # صفحات المصادقة
│   ├── courses/        # صفحات الكورسات
│   ├── dashboard/      # لوحات التحكم
│   └── errors/         # صفحات الأخطاء
├── static/             # الملفات الثابتة
│   ├── css/           # ملفات CSS
│   ├── js/            # ملفات JavaScript
│   └── images/        # الصور
├── bot/               # بوت التليجرام
│   └── main.py       # الملف الرئيسي للبوت
└── utils/            # وظائف مساعدة
```

## 🔧 الإعدادات المطلوبة

### متغيرات البيئة (.env)
```env
# إعدادات Flask
FLASK_SECRET_KEY=your_secret_key
FLASK_ENV=development
FLASK_DEBUG=True

# إعدادات Firebase
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY=your_private_key
FIREBASE_CLIENT_EMAIL=your_client_email
FIREBASE_DATABASE_URL=your_database_url

# إعدادات بوت التليجرام
TELEGRAM_BOT_TOKEN=your_bot_token
BOT_OWNER_TELEGRAM_ID=your_telegram_id

# إعدادات المنصة
PLATFORM_URL=http://localhost:5000
PLATFORM_NAME=منصة الكورسات التعليمية
```

## 🎨 الميزات المطورة

### ✅ المكتمل
- [x] إعداد مشروع Flask الأساسي
- [x] هيكل المجلدات والملفات
- [x] القوالب الأساسية مع Bootstrap 5
- [x] التصميم المتجاوب والألوان
- [x] بوت التليجرام الأساسي
- [x] صفحات الأخطاء (404, 500, 403)
- [x] نظام التنقل والقوائم

### 🔄 قيد التطوير
- [ ] تكامل Firebase Realtime Database
- [ ] نظام المصادقة مع Firebase Auth
- [ ] إدارة التخصصات والكورسات
- [ ] مشغل الفيديو المتقدم
- [ ] نظام أكواد التفعيل
- [ ] روابط الدعوة والتسجيل

## 🛠️ التطوير

### إضافة ميزة جديدة
1. إنشاء branch جديد
2. تطوير الميزة
3. اختبار الوظائف
4. إنشاء Pull Request

### معايير الكود
- اتباع PEP 8 للـ Python
- تعليقات واضحة باللغة العربية
- اختبار جميع الوظائف
- توثيق التغييرات

## 📞 الدعم والتواصل

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل عبر التليجرام
- مراجعة الوثائق

## 📄 الترخيص

هذا المشروع مرخص تحت [MIT License](LICENSE)

---

**تم تطوير هذا المشروع بواسطة فريق منصة الكورسات التعليمية** 🎓
