{% extends "base.html" %}

{% block title %}{{ course.title }} - {{ platform_name }}{% endblock %}

{% block extra_css %}
<style>
.lesson-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

.lesson-item:hover {
    background-color: #f8f9fa;
    transform: translateX(-5px);
}

.lesson-item.active {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.lesson-item.completed {
    background-color: #e8f5e8;
    border-left: 4px solid #4caf50;
}

.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.lesson-progress-bar {
    height: 4px;
    background-color: #2196f3;
    transition: width 0.3s ease;
}

.course-progress {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- قائمة الدروس -->
        <div class="col-lg-4 col-xl-3">
            <div class="card border-0 shadow-sm sticky-top" style="top: 20px;">
                <!-- معلومات الكورس -->
                <div class="card-header course-progress text-white">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-book me-2"></i>
                        <h6 class="mb-0">{{ course.title }}</h6>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <small>التقدم الإجمالي</small>
                        <small id="overallProgress">0%</small>
                    </div>
                    <div class="progress mt-2" style="height: 6px;">
                        <div class="progress-bar bg-white" id="overallProgressBar" style="width: 0%"></div>
                    </div>
                </div>
                
                <!-- قائمة الدروس -->
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="lessonsList">
                        <!-- سيتم تحميل الدروس هنا -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- محتوى الدرس -->
        <div class="col-lg-8 col-xl-9">
            <div class="card border-0 shadow-sm">
                <div class="card-body" id="lessonContent">
                    <!-- رسالة ترحيب -->
                    <div class="text-center py-5" id="welcomeMessage">
                        <i class="fas fa-play-circle fa-5x text-primary mb-4"></i>
                        <h3 class="text-primary">مرحباً بك في الكورس</h3>
                        <p class="text-muted">اختر درساً من القائمة الجانبية لبدء المشاهدة</p>
                        <div class="mt-4">
                            <h5 class="text-muted">معلومات الكورس</h5>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-user text-primary me-2"></i>
                                        <span>المدرس: {{ course.instructor_name }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-layer-group text-primary me-2"></i>
                                        <span>
                                            {% if course.is_general %}
                                                كورس عام
                                            {% else %}
                                                المرحلة {{ course.stage }}
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            {% if course.description %}
                            <div class="mt-3">
                                <p class="text-muted">{{ course.description }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/video-progress.js') }}"></script>
<script>
let currentCourse = null;
let currentLesson = null;
let lessons = [];

$(document).ready(function() {
    currentCourse = {
        id: '{{ course.id }}',
        title: '{{ course.title }}',
        instructor_name: '{{ course.instructor_name }}'
    };
    
    loadLessons();
});

async function loadLessons() {
    try {
        const response = await fetch(`/api/instructor/courses/${currentCourse.id}/lessons`);
        const data = await response.json();
        
        if (data.success) {
            lessons = data.lessons || [];
            displayLessons(lessons);
            updateOverallProgress();
        } else {
            showAlert('فشل في تحميل الدروس: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('خطأ في تحميل الدروس:', error);
        showAlert('حدث خطأ في تحميل الدروس', 'danger');
    }
}

function displayLessons(lessons) {
    const container = $('#lessonsList');
    
    if (!lessons || lessons.length === 0) {
        container.html(`
            <div class="text-center py-4">
                <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">لا توجد دروس</h6>
                <p class="text-muted small">لم يتم إضافة دروس لهذا الكورس بعد</p>
            </div>
        `);
        return;
    }
    
    let html = '';
    
    lessons.forEach(function(lesson, index) {
        const isCompleted = false; // سيتم تحديثه لاحقاً من بيانات التقدم
        const isActive = false;
        
        html += `
            <div class="list-group-item lesson-item ${isCompleted ? 'completed' : ''} ${isActive ? 'active' : ''}" 
                 data-lesson-id="${lesson.id}" onclick="loadLesson('${lesson.id}')">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center">
                            <div class="lesson-status-icon me-2">
                                ${isCompleted ? 
                                    '<i class="fas fa-check-circle text-success"></i>' : 
                                    '<i class="fas fa-play-circle text-primary"></i>'
                                }
                            </div>
                            <div>
                                <h6 class="mb-1">${lesson.title}</h6>
                                <small class="text-muted">
                                    ${lesson.content_type === 'video' ? 
                                        '<i class="fas fa-video me-1"></i>فيديو' : 
                                        '<i class="fas fa-file-text me-1"></i>نص'
                                    }
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="lesson-number">
                        <span class="badge bg-light text-dark">${index + 1}</span>
                    </div>
                </div>
                
                <!-- شريط التقدم للدرس -->
                <div class="progress mt-2" style="height: 3px;">
                    <div class="progress-bar lesson-progress-bar" style="width: 0%"></div>
                </div>
            </div>
        `;
    });
    
    container.html(html);
}

async function loadLesson(lessonId) {
    try {
        // تحديث الدرس النشط في القائمة
        $('.lesson-item').removeClass('active');
        $(`.lesson-item[data-lesson-id="${lessonId}"]`).addClass('active');
        
        // البحث عن الدرس
        const lesson = lessons.find(l => l.id === lessonId);
        if (!lesson) {
            showAlert('الدرس غير موجود', 'danger');
            return;
        }
        
        currentLesson = lesson;
        
        // عرض محتوى الدرس
        displayLessonContent(lesson);
        
    } catch (error) {
        console.error('خطأ في تحميل الدرس:', error);
        showAlert('حدث خطأ في تحميل الدرس', 'danger');
    }
}

function displayLessonContent(lesson) {
    const container = $('#lessonContent');
    
    let html = `
        <div class="lesson-header mb-4">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h4 class="text-primary">${lesson.title}</h4>
                    <div class="d-flex align-items-center text-muted">
                        <i class="fas fa-${lesson.content_type === 'video' ? 'video' : 'file-text'} me-2"></i>
                        <span>${lesson.content_type === 'video' ? 'درس فيديو' : 'درس نصي'}</span>
                    </div>
                </div>
                <div>
                    <button class="btn btn-outline-success btn-sm" onclick="markLessonAsCompleted('${currentCourse.id}', '${lesson.id}')">
                        <i class="fas fa-check me-1"></i>تم الإكمال
                    </button>
                </div>
            </div>
            
            <!-- شريط التقدم للدرس الحالي -->
            <div class="mt-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <small class="text-muted">تقدم الدرس</small>
                    <small class="text-muted lesson-progress-text">0% مكتمل</small>
                </div>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar lesson-progress-bar" style="width: 0%"></div>
                </div>
            </div>
        </div>
        
        <div class="lesson-body">
    `;
    
    if (lesson.content_type === 'video' && lesson.content_data && lesson.content_data.video_url) {
        html += `
            <div class="video-container mb-4">
                <video id="lessonVideo" controls preload="metadata" class="w-100">
                    <source src="${lesson.content_data.video_url}" type="video/mp4">
                    متصفحك لا يدعم تشغيل الفيديو.
                </video>
            </div>
        `;
    }
    
    if (lesson.content_data && lesson.content_data.description) {
        html += `
            <div class="lesson-description">
                <h5>وصف الدرس</h5>
                <div class="content-text">
                    ${lesson.content_data.description}
                </div>
            </div>
        `;
    }
    
    html += '</div>';
    
    container.html(html);
    
    // إخفاء رسالة الترحيب
    $('#welcomeMessage').hide();
    
    // تهيئة تتبع الفيديو إذا كان موجوداً
    if (lesson.content_type === 'video') {
        const videoElement = document.getElementById('lessonVideo');
        if (videoElement) {
            initVideoTracker(videoElement, lesson.id, currentCourse.id);
        }
    }
}

function updateOverallProgress() {
    // حساب التقدم الإجمالي (سيتم تحديثه لاحقاً من بيانات التقدم الفعلية)
    const completedLessons = $('.lesson-item.completed').length;
    const totalLessons = lessons.length;
    const percentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;
    
    $('#overallProgress').text(Math.round(percentage) + '%');
    $('#overallProgressBar').css('width', percentage + '%');
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.container-fluid').first().prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
