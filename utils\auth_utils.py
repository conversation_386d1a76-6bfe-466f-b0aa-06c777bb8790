"""
وحدة المصادقة والأمان
Authentication and Security Module

هذه الوحدة مسؤولة عن:
- التحقق من بيانات المستخدم
- تشفير وفحص كلمات المرور
- التكامل مع Firebase للمصادقة
- إدارة جلسات المستخدمين
"""

import hashlib
import secrets
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Tuple
from werkzeug.security import generate_password_hash, check_password_hash
from utils.firebase_utils import get_firebase_manager
from utils.jwt_utils import get_jwt_manager

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

class AuthManager:
    """مدير المصادقة والأمان"""
    
    def __init__(self):
        self.firebase_manager = get_firebase_manager()
        self.jwt_manager = get_jwt_manager()
    
    def hash_password(self, password: str) -> str:
        """
        تشفير كلمة المرور
        Hash password using secure method
        
        Args:
            password: كلمة المرور الخام
            
        Returns:
            كلمة المرور المشفرة
        """
        try:
            return generate_password_hash(password, method='pbkdf2:sha256', salt_length=16)
        except Exception as e:
            logger.error(f"خطأ في تشفير كلمة المرور: {e}")
            raise
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """
        التحقق من كلمة المرور
        Verify password against hash
        
        Args:
            password: كلمة المرور الخام
            hashed_password: كلمة المرور المشفرة
            
        Returns:
            True إذا كانت صحيحة، False إذا كانت خاطئة
        """
        try:
            return check_password_hash(hashed_password, password)
        except Exception as e:
            logger.error(f"خطأ في التحقق من كلمة المرور: {e}")
            return False
    
    def generate_random_password(self, length: int = 12) -> str:
        """
        توليد كلمة مرور عشوائية
        Generate random password
        
        Args:
            length: طول كلمة المرور (افتراضي 12)
            
        Returns:
            كلمة مرور عشوائية
        """
        try:
            # أحرف مسموحة لكلمة المرور
            alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
            password = ''.join(secrets.choice(alphabet) for _ in range(length))
            return password
        except Exception as e:
            logger.error(f"خطأ في توليد كلمة المرور: {e}")
            raise
    
    def authenticate_user(self, email: str, password: str) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """
        مصادقة المستخدم
        Authenticate user with email and password

        Args:
            email: البريد الإلكتروني
            password: كلمة المرور

        Returns:
            (نجح المصادقة، بيانات المستخدم، رسالة الخطأ)
        """
        try:
            # التحقق من صحة البيانات المدخلة
            if not email or not password:
                return False, None, "البريد الإلكتروني وكلمة المرور مطلوبان"

            # البحث عن المستخدم في قاعدة البيانات
            user = self.firebase_manager.get_user_by_email(email)
            if not user:
                logger.warning(f"محاولة تسجيل دخول بإيميل غير موجود: {email}")
                return False, None, "البريد الإلكتروني أو كلمة المرور غير صحيحة"

            # التحقق من كلمة المرور
            stored_password = user.get('password')
            if not stored_password:
                logger.error(f"كلمة المرور غير موجودة للمستخدم: {email}")
                return False, None, "خطأ في بيانات المستخدم"

            # التحقق من كلمة المرور (دعم للكلمات المشفرة وغير المشفرة)
            password_valid = False
            if stored_password.startswith('pbkdf2:sha256:'):
                # كلمة مرور مشفرة (للحسابات القديمة فقط)
                password_valid = self.verify_password(password, stored_password)
            else:
                # كلمة مرور غير مشفرة (النظام الافتراضي الجديد)
                password_valid = (password == stored_password)

            if not password_valid:
                logger.warning(f"محاولة تسجيل دخول بكلمة مرور خاطئة: {email}")
                return False, None, "البريد الإلكتروني أو كلمة المرور غير صحيحة"

            # التحقق من حالة المستخدم
            if user.get('status') == 'inactive':
                return False, None, "حسابك غير مفعل، يرجى التواصل مع الإدارة"

            if user.get('status') == 'banned':
                return False, None, "تم حظر حسابك، يرجى التواصل مع الإدارة"

            logger.info(f"تم تسجيل دخول المستخدم بنجاح: {email}")
            return True, user, "تم تسجيل الدخول بنجاح"

        except Exception as e:
            logger.error(f"خطأ في مصادقة المستخدم: {e}")
            return False, None, "حدث خطأ في النظام، يرجى المحاولة لاحقاً"
    
    def create_user_session(self, user_data: Dict[str, Any]) -> Optional[str]:
        """
        إنشاء جلسة للمستخدم
        Create user session with JWT token
        
        Args:
            user_data: بيانات المستخدم
            
        Returns:
            JWT token أو None
        """
        try:
            # تحضير بيانات التوكين
            token_data = {
                'user_id': user_data.get('user_id'),
                'email': user_data.get('email'),
                'role': user_data.get('role'),
                'telegram_id': user_data.get('telegram_id'),
                'first_name': user_data.get('first_name'),
                'last_name': user_data.get('last_name'),
                'specialization_id': user_data.get('specialization_id')
            }
            
            # إنشاء JWT token
            token = self.jwt_manager.generate_token(token_data)
            
            # تحديث آخر تسجيل دخول
            from datetime import datetime, timezone
            self.firebase_manager.update_user(user_data['user_id'], {
                'last_login': datetime.now(timezone.utc).isoformat()
            })
            
            logger.info(f"تم إنشاء جلسة للمستخدم: {user_data.get('email')}")
            return token
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء جلسة المستخدم: {e}")
            return None
    
    def validate_session(self, token: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        التحقق من صحة الجلسة
        Validate user session
        
        Args:
            token: JWT token
            
        Returns:
            (صحة الجلسة، بيانات المستخدم)
        """
        try:
            user_data = self.jwt_manager.get_user_from_token(token)
            if not user_data:
                return False, None
            
            # التحقق من وجود المستخدم في قاعدة البيانات
            user = self.firebase_manager.get_user(user_data['user_id'])
            if not user:
                logger.warning(f"مستخدم غير موجود في قاعدة البيانات: {user_data['user_id']}")
                return False, None
            
            # التحقق من حالة المستخدم
            if user.get('status') in ['inactive', 'banned']:
                logger.warning(f"مستخدم غير مفعل أو محظور: {user_data['email']}")
                return False, None
            
            return True, user_data
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الجلسة: {e}")
            return False, None
    
    def logout_user(self, token: str) -> bool:
        """
        تسجيل خروج المستخدم
        Logout user (invalidate session)
        
        Args:
            token: JWT token
            
        Returns:
            True إذا نجح تسجيل الخروج
        """
        try:
            user_data = self.jwt_manager.get_user_from_token(token)
            if user_data:
                logger.info(f"تم تسجيل خروج المستخدم: {user_data.get('email')}")
            
            # في JWT، لا يمكن إلغاء التوكين مباشرة
            # لكن يمكن إضافة التوكين لقائمة سوداء إذا لزم الأمر
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الخروج: {e}")
            return False
    
    def change_password(self, user_id: str, old_password: str, new_password: str) -> Tuple[bool, str]:
        """
        تغيير كلمة المرور
        Change user password
        
        Args:
            user_id: معرف المستخدم
            old_password: كلمة المرور القديمة
            new_password: كلمة المرور الجديدة
            
        Returns:
            (نجح التغيير، رسالة)
        """
        try:
            # الحصول على بيانات المستخدم
            user = self.firebase_manager.get_user(user_id)
            if not user:
                return False, "المستخدم غير موجود"
            
            # التحقق من كلمة المرور القديمة
            if not self.verify_password(old_password, user.get('password', '')):
                return False, "كلمة المرور القديمة غير صحيحة"
            
            # تشفير كلمة المرور الجديدة
            hashed_new_password = self.hash_password(new_password)
            
            # تحديث كلمة المرور
            success = self.firebase_manager.update_user(user_id, {
                'password': hashed_new_password,
                'password_changed_at': datetime.now(timezone.utc).isoformat()
            })
            
            if success:
                logger.info(f"تم تغيير كلمة المرور للمستخدم: {user.get('email')}")
                return True, "تم تغيير كلمة المرور بنجاح"
            else:
                return False, "فشل في تحديث كلمة المرور"
            
        except Exception as e:
            logger.error(f"خطأ في تغيير كلمة المرور: {e}")
            return False, "حدث خطأ في النظام"
    
    def reset_password(self, user_id: str, new_password: str, encrypt: bool = False) -> Tuple[bool, str]:
        """
        إعادة تعيين كلمة المرور (للأدمن)
        Reset user password (admin only)

        Args:
            user_id: معرف المستخدم
            new_password: كلمة المرور الجديدة
            encrypt: هل يتم تشفير كلمة المرور (افتراضي: False)

        Returns:
            (نجح التغيير، رسالة)
        """
        try:
            # تحديد كلمة المرور (مشفرة أو غير مشفرة)
            password_to_store = self.hash_password(new_password) if encrypt else new_password

            # تحديث كلمة المرور
            success = self.firebase_manager.update_user(user_id, {
                'password': password_to_store,
                'password_reset_at': datetime.now(timezone.utc).isoformat(),
                'password_reset_required': True,
                'password_encrypted': encrypt
            })

            if success:
                user = self.firebase_manager.get_user(user_id)
                logger.info(f"تم إعادة تعيين كلمة المرور للمستخدم: {user.get('email') if user else user_id}")
                return True, "تم إعادة تعيين كلمة المرور بنجاح"
            else:
                return False, "فشل في إعادة تعيين كلمة المرور"

        except Exception as e:
            logger.error(f"خطأ في إعادة تعيين كلمة المرور: {e}")
            return False, "حدث خطأ في النظام"

    def create_user_with_plain_password(self, user_data: Dict[str, Any], password: str) -> Optional[str]:
        """
        إنشاء مستخدم جديد مع كلمة مرور غير مشفرة
        Create new user with plain text password

        Args:
            user_data: بيانات المستخدم
            password: كلمة المرور (غير مشفرة)

        Returns:
            معرف المستخدم الجديد أو None
        """
        try:
            # إضافة كلمة المرور غير المشفرة
            user_data['password'] = password
            user_data['password_encrypted'] = False
            user_data['created_at'] = datetime.now(timezone.utc).isoformat()
            user_data['updated_at'] = user_data['created_at']

            # إنشاء المستخدم في Firebase
            user_id = self.firebase_manager.create_user(user_data)

            if user_id:
                logger.info(f"تم إنشاء مستخدم جديد بكلمة مرور غير مشفرة: {user_data.get('email')}")
                return user_id
            else:
                logger.error(f"فشل في إنشاء المستخدم: {user_data.get('email')}")
                return None

        except Exception as e:
            logger.error(f"خطأ في إنشاء المستخدم: {e}")
            return None

# إنشاء مثيل مشترك
auth_manager = AuthManager()

def get_auth_manager() -> AuthManager:
    """الحصول على مثيل Auth Manager"""
    return auth_manager
